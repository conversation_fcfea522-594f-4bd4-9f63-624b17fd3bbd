<template>
    <div class="categories">
        <template v-for="item in categories" :key="item.id">
            <div class="item">
                <img :src="item.pictureUrl" alt="">
                <span class="text">{{ item.title }}</span>
            </div>
        </template>
    </div>
</template>

<script setup>
import useHomeStore from '@/stores/modules/home';
import { storeToRefs } from 'pinia';
const categoriesStore = useHomeStore()
const { categories } = storeToRefs(categoriesStore)
</script>

<style lang="less" scoped>
.categories {
    display: flex;
    height: 80px;
    overflow-x: auto;
    // margin-left: 4%;
    padding: 0 10px;

    &::-webkit-scrollbar {
        display: none;
    }

    .item {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 70px;
        text-align: center;



        img {
            width: 32px;
            height: 32px;
        }

        .text {
            font-style: 12px;
            margin-top: 12px;
        }

    }

}
</style>