<template>
    <div class="home" ref="homeRef">
        <homeNavBar></homeNavBar>
        <div class="banner">
            <img src="@/assets/img/home/<USER>" alt="">
        </div>
        <!-- <homeSearchBox :hotSuggests="hotSuggests"></homeSearchBox> -->
        <!-- 搜索框以及定位日历 -->
        <homeSearchBox></homeSearchBox>
        <!-- 分类模块 -->
        <homeCategories></homeCategories>

        <!-- 我是搜索框的内容 -->
        <div class="search-btn" v-if="isShowSearchBar">
            <searchBar></searchBar>
        </div>

        <!-- 热门精选 -->
        <homeContent></homeContent>
        <!-- <button @click="Btnclick">加载更多</button> -->
    </div>
</template>
<script>
export default {
    name: "home"
}
</script>

<script setup>
import { ref, onUnmounted, onMounted, watch, onActivated } from 'vue';
import homeNavBar from './components/home-nav-bar.vue';
import homeSearchBox from './components/home-search-box.vue'
// import hyRequest from '@/services/request/index'
import useHomeStore from '@/stores/modules/home';
import homeCategories from './components/home-categories.vue';
import homeContent from './components/home-content.vue';
import { useScroll } from '@/hooks/useScroll'
import searchBar from '@/components/search-bar/search-bar.vue';
//发送网络请求
//1、热门建议

const homeStore = useHomeStore()
//发送推荐城市网络请求
homeStore.fetchHotSuggestsData()
//发送推荐分类网络请求
homeStore.fetchHotCategoriesData()
//发送精选网络请求
// let currentPage = 1
homeStore.fetchHouselistData()
// function Btnclick() {
//     homeStore.fetchHouselistData()
// }

const homeRef = ref()
const { isReachBottom, scrollTop } = useScroll(homeRef)
watch(isReachBottom, (newValue) => {
    if (newValue) {
        homeStore.fetchHouselistData().then(() => {
            console.log("滚动到底部");
            isReachBottom.value = false;
        })
    }
})

// 搜索框显示隐藏的控制的控制
const isShowSearchBar = ref(false)
watch(scrollTop, (newValue) => {
    // console.log(newValue);
    isShowSearchBar.value = newValue >= 450
})



// 跳转回home时，保留原来的位置
onActivated(() => {
    homeRef.value?.scrollTo({
        top: scrollTop.value
    })
})
//定义的可响应数据，依赖另一个可响应式的数据，那么可以使用计算属性computed
// const isShowSearchBar = computed(() => {
//     return scrollTop.value >= 100
// })

// 跳转回home时，保留原来的位置


</script>

<style lang="less" scoped>
.home {
    height: 100vh;
    overflow-y: auto;
    box-sizing: border-box;
    padding-bottom: 60px;
}

.search-btn {
    position: fixed;
    z-index: 9;
    top: 0;
    right: 0;
    left: 0;
    height: 45px;
    padding: 16px 16px 10px;
    background-color: #fff
}

.banner {
    img {
        width: 100%;
    }
}
</style>