<template>
    <div class="search">
        <!-- 位置信息 -->
        <div class="location bottom-gray-line">
            <div class="city" @click="cityClick">{{ currentCity.cityName }}</div>
            <div class="position" @click="positionClick">
                <span class="text">我的位置</span>
                <img src="@/assets/img/home/<USER>" alt="">
            </div>
        </div>

        <!-- 日期范围 -->
        <div class="section date-range bottom-gray-line" @click="showCalendar = true">
            <!-- 前一天 -->
            <div class="start">
                <div class="date">
                    <span class="tip">入住</span>
                    <span class="time">{{ startDateStr }}</span>
                </div>
            </div>
            <!-- 共多少天 -->
            <div class="stay"> 共{{ stayCount }}晚 </div>
            <!-- 后一天 -->
            <div class="end">
                <div class="date">
                    <span class="tip">离店</span>
                    <span class="time">{{ endDateStr }}</span>
                </div>
            </div>
        </div>
        <!-- 日期选择 -->
        <!-- <van-cell title="选择日期区间" :value="date" @click="show = true" /> -->
        <van-calendar v-model:show="showCalendar" type="range" color="#ff9854" :round="false" @confirm="onConfirm" />

        <!-- 价格/人数选择 -->
        <div class="section price-counter bottom-gray-line bottom-gray-line">
            <div class="start">价格不限</div>
            <div class="end">人数不限</div>
        </div>
        <!-- 关键字 -->
        <div class="section keyword bottom-gray-line bottom-gray-line">关键字/位置/民宿名</div>

        <!-- 热门建议 -->
        <div class="section hotSuggests">
            <div v-for="(item, index) in hotSuggests" :key="index" class="item"
                :style="{ color: item.tagText.color, background: item.tagText.background.color }">
                {{ item.tagText.text }}
            </div>
        </div>

        <!-- 搜索按钮 -->
        <div class="section search-btn" @click="searchBtnClick">
            <div class="btn">搜索</div>
        </div>
    </div>
</template>

<script setup>
import { ref,computed } from 'vue';
import useCityStore from '@/stores/modules/city';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { formatMouthDay, getDiffDays } from '@/utils/format_date'
import useHomeStore from '@/stores/modules/home'
import useMainStore from '@/stores/modules/main';
const router = useRouter()



// defineProps({
//     hotSuggests: {
//         type: Array,
//         default: () => []
//     }
// })

// 位置/城市
const cityClick = () => {
    router.push("/city")
    console.log("cityClick");
}
// 获取当前位置
const positionClick = () => {
    navigator.geolocation.getCurrentPosition(res => {
        console.log("获取位置成功", res);
    }, err => {
        console.log("获取位置失败", err);
    })
}
// 获取当前城市
const cityStore = useCityStore()
const { currentCity } = storeToRefs(cityStore)


// 日期范围的处理
const mainStore = useMainStore()
const { startDate, endDate } = storeToRefs(mainStore)
const startDateStr = computed(() => formatMouthDay(startDate.value))
const endDateStr = computed(() => formatMouthDay(endDate.value))
// 计算选择了多少天
const stayCount = ref(getDiffDays(startDate.value, endDate.value))
//选择日期的处理
const showCalendar = ref(false)
const onConfirm = (value) => {
    //设置日期
    const selectStartDate = value[0];
    const selectEndDate = value[1];
    mainStore.startDate=selectStartDate
    mainStore.endDate=selectEndDate
    // startDate.value = formatMouthDay(selectStartDate)
    // endDate.value = formatMouthDay(selectEndDate)
    stayCount.value = getDiffDays(selectStartDate, selectEndDate)
    //隐藏选择日志框
    showCalendar.value = false
}

// 热门建议
const HomeStore = useHomeStore()
const { hotSuggests } = storeToRefs(HomeStore)

//全局搜索
const searchBtnClick = () => {
    router.push({
        path: "/search",
        query: {
            startDate: startDate.value,
            endDate: endDate.value,
            currentCity: currentCity.value.cityName
        }
    })
}

</script>

<!-- css -->
<style lang="less" scoped>
// 局部修改组件属性
.search {
    // margin-bottom: 10px;
    // display: flex;
    --van-calendar-popup-height: 100%;
}

// 位置信息
.location {
    display: flex;
    align-items: center;
    height: 44px;
    padding: 0 20px;

    .city {
        flex: 1;
        font-size: 12px;
    }

    .position {
        width: 74px;
        display: flex;
        align-items: center;

        .text {
            font-size: 12px;
        }

        img {
            margin-left: 5px;
            width: 18px;
            height: 18px;
            // vertical-align: middle;
        }
    }
}

.section {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 0 20px;
    color: #999;
    height: 44px;

    .start {
        flex: 1;
        display: flex;
        height: 44px;
        align-items: center;
    }

    .end {
        min-width: 30%;
        padding-left: 20px;
    }

    .date {
        display: flex;
        flex-direction: column;

        .tip {
            font-size: 12px;
            color: #999;
        }

        .time {
            margin-top: 3px;
            color: #333;
            font-size: 15px;
            font-weight: 500;
        }
    }
}

.date-range {
    height: 44px;

    .stay {
        flex: 1;
        text-align: center;
        font-size: 12px;
        color: #666;
    }
}

.price-counter {
    .start {
        border-right: 1px solid var(--line-color);
    }
}

.hotSuggests {
    margin: 10px 0;
    height: auto;

    .item {
        padding: 4px 8px;
        margin: 4px;
        border-radius: 14px;
        font-size: 12px;
        line-height: 1;
    }

}

.search-btn {
    // margin-bottom: 50px;
    // margin-bottom: 4%;

    .btn {
        // position: relative;
        // z-index: 9;
        width: 100%;
        // width: 342px;
        height: 38px;
        min-height: 38px;
        font-weight: 500;
        font-size: 18px;
        line-height: 38px;
        text-align: center;
        border-radius: 20px;
        color: #fff;
        background-image: var(--theme-linear-gradient);

    }

}
</style>