<template>
    <div class="tabbar-container">
        <div class="tab-bar">
            <van-tabbar v-model="currentIndex" active-color="#ff9854" route>
                <template v-for="(item, index) in tabbarData" :key="item">
                    <van-tabbar-item :to="item.path">
                        <span>{{ item.text }}</span>
                        <template #icon>
                            <div>
                                <img v-if="currentIndex !== index" :src="getAssetURL(item.image)" alt="">
                                <img v-else :src="getAssetURL(item.imageActive)" alt="">
                            </div>
                        </template>
                    </van-tabbar-item>
                </template>
            </van-tabbar>
        </div>
    </div>
</template>
<script setup>
import tabbarData from "@/assets/data/tabbar"
import { getAssetURL } from "@/utils/load-assets"
import { ref, watch } from "vue";
import { useRoute } from "vue-router";

const currentIndex = ref(0)
const route = useRoute()
// 监听路由改变时，找到对应的索引，设置currentindex
watch(route, (newRoute) => {
    const index = tabbarData.findIndex(item => item.path === newRoute.path)
    if (index === -1) return
    currentIndex.value = index
})

// const itemClick = (index, item) => {   // 相当于 function itemClick(){}
//     // currentIndex.value = index
//     router.push(item.path)
// }
</script>

<style lang="less" scoped>
.tabbar-container {
    height: 50px;
}

.tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    display: flex;
    border-top: 1px solid #f3f3f3;




    // .tab-bar-item {
    //     flex: 1;
    //     display: flex;
    //     flex-direction: column;
    //     justify-content: center;
    //     align-items: center;

    //     &.active {
    //         // color: var(--primary-color);
    //         color: #ff9854;
    //     }


    //     img {
    //         width: 36px;
    //     }

    //     .text {
    //         font-style: 12px;
    //         margin-top: 2px;
    //     }

    // }
}
</style>