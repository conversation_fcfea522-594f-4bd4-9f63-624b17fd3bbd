<template>
    <div class="city-group">
        <van-index-bar :index-list="indexList">
            <van-index-anchor index="热门" />
            <div class="list">
                <template v-for="(city, index) in groupData.hotCities" :key="city">
                    <div class="city" @click="cityClick(city)">
                        {{ city.cityName }}
                    </div>
                </template>
            </div>
            <!-- 港澳台海外数据展示 -->
            <div v-for="(group, index) in groupData.cities" :key="index">
                <van-index-anchor :index="group.group" /> <!-- 标题 ABCD -->
                <template v-for="(city, indey) in group.cities" :key="indey">
                    <van-cell :title="city.cityName" @click="cityClick(city)" /> <!-- 标题ABCD下面的内容 -->
                </template>
            </div>
        </van-index-bar>




        <!-- <template v-for="(group, index) in groupData.cities" :key="index">
            <div class="group-item">
                <h2 class="title">标题：{{ group.group }}</h2>
                <div class="list">
                    <template v-for="(city, indey) in group.cities" :key="indey">
                        <div class="city">{{ city.cityName }}</div>
                    </template>
                </div>
            </div>
        </template> -->
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import useCityStore from '@/stores/modules/city'
// 定义props
const props = defineProps({
    groupData: {
        type: Object,
        default: ({})
    }
})
//定义索引
const indexList = computed(() => {
    const list = props.groupData.cities.map(item => item.group)
    list.unshift("#")

    return list
})


const router = useRouter()
const cityStore = useCityStore()
// 监听城市的点击
const cityClick = (city) => {
    // console.log(city.cityName);
    cityStore.currentCity = city
    router.back()
}


</script>

<style lang="less" scoped>
.list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    padding: 10px;
    padding-right: 25px;

    .city {
        width: 70px;
        height: 28px;
        border-radius: 14px;
        font-size: 12px;
        color: #000;
        text-align: center;
        line-height: 28px;
        background-color: #fff4ec;
        margin-bottom: 10px;
    }


}
</style>