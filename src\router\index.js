import { createRouter } from "vue-router";
import { createWebHashHistory } from "vue-router";

const router = createRouter({
    history: createWebHashHistory(),
    routes: [
        {
            path: "/",
            redirect: "/home"
        },
        {
            path: "/home",
            component: () => import("@/views/home/<USER>")
        },
        {
            path: "/order",
            component: () => import("@/views/order/order.vue")
        },
        {
            path: "/message",
            component: () => import("@/views/message/message.vue")
        },
        {
            path: "/favor",
            component: () => import("@/views/favor/favor.vue")
        },
        {
            path: "/city",
            component: () => import("@/views/city/city.vue")
            // meta:{
            //     hideTabBar:true
            // }
        }, {
            path: "/search",
            component: () => import("@/views/search/search.vue"),
            meta: {
                hideTabBar: true
            }
        },
        {
            //动态路由传递参数
            path: "/detail/:id",
            component: () => import("@/views/Detail/Detail.vue"),
            meta: {
                hideTabBar: true
            }
        }

    ]

})

export default router