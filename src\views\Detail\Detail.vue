<template>
    <div class="detail" ref="detailRef">
        <tabControl class="tabs" v-if="showTabControl" :titles="names" @tabItemClick="tabClick" ref="tabControlRef" />
        <van-nav-bar title="房屋详情" left-text="返回旅途" left-arrow @click-left="onClickLeft" />
    </div>
    <div class="main" v-if="mainPart" v-memo="[mainPart]">
        <!-- 轮播图 -->
        <detailSwipe :swipeData="mainPart.topModule.housePicture.housePics"></detailSwipe>
        <!-- 标题 -->
        <detailinfos name="描述" :ref="getSectionRef" :topInfos="mainPart.topModule"></detailinfos>
        <!-- 房屋设施 -->
        <detailFacility name="房屋设施" :ref="getSectionRef"
            :houseFacility="mainPart.dynamicModule.facilityModule.houseFacility">
        </detailFacility>
        <!-- 房东介绍 -->
        <detailLandlord name="房东介绍" :ref="getSectionRef" :landlord="mainPart.dynamicModule.landlordModule"></detailLandlord>
        <!-- 热门评论 -->
        <detailComment name="热门评论" :ref="getSectionRef" :comment="mainPart.dynamicModule.commentModule"></detailComment>
        <!-- 预订须知 -->
        <detailNotice name="预订须知" :ref="getSectionRef" :order-rules="mainPart.dynamicModule.rulesModule.orderRules">
        </detailNotice>
        <!-- 地图模块 -->
        <detailMap name="周边" :position="mainPart.dynamicModule.positionModule"></detailMap>
        <!-- 价格模块 -->
        <detail-intro :price-intro="mainPart.introductionModule" />
    </div>
    <div class="footer">
        <img src="@/assets/img/detail/icon_ensure.png" alt="">
        <div class="text">弘源旅途, 永无止境!</div>
    </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import tabControl from '@/components/tab-control/tab-control.vue';
import { useRouter, useRoute } from 'vue-router';
import { getDetailInfos } from '@/services'
import detailSwipe from '@/views/Detail/components/detail_01_swipe.vue'
import detailinfos from './components/detail_02_infos.vue';
import detailFacility from './components/detail_03-facility.vue'
import detailLandlord from './components/detail_04_landlord.vue';
import detailComment from './components/detail_05_comment.vue';
import detailNotice from './components/detail_06_notice.vue';
import detailMap from './components/detail_07_map.vue';
import detailIntro from './components/detail_08-intro.vue';
import { useScroll } from '@/hooks/useScroll';
const router = useRouter()
const route = useRoute()
const houseId = route.params.id
const detailInfos = ref({})
//发送网络请求
const mainPart = computed(() => detailInfos.value.mainPart)
getDetailInfos(houseId).then(res => {
    detailInfos.value = res.data
    // console.log(res);
})

// 返回上一层
const onClickLeft = () => {
    router.back()
}

//tabControl相关操作
const detailRef = ref()
const { scrollTop } = useScroll(detailRef)
const showTabControl = computed(() => {
    return scrollTop.value >= 300
})

// 获取各个组件的ref
const sectionEls = ref({})
const names = computed(() => {
    return Object.keys(sectionEls.value)

})

const getSectionRef = (value) => {
    if (!value) return
    // console.log(value);  //打印：Proxy(Object)
    const name = value.$el.getAttribute("name")
    // console.log(name);  打印：描述 房屋设施。。。
    sectionEls.value[name] = value.$el
    // console.log(sectionEls);
}

let isClick = false
let currentdistance = -1
const tabClick = (index) => {
    const key = Object.keys(sectionEls.value)[index]   // 打印点击的属性名字
    const el = sectionEls.value[key]        //获取整个元素
    let distance = el.offsetTop
    if (index !== 0) {
        distance = distance - 44
    }
    isClick = true;
    currentdistance = distance
    window.scrollTo({
        top: distance,
        behavior: "smooth"
    })
    // detailRef.value.scrollTo({
    //     top: 300,
    //     behavior: "smooth"
    // })
}

// 页面滚动，匹配对应的tabControll的index
const tabControlRef = ref()
watch(scrollTop, (newValue) => {
    if (newValue == currentdistance) {
        isClick = false
    }
    if (isClick) return
    const els = Object.values(sectionEls.value) //返回以values为元素的数组
    // console.log(els); 获取每个元素的div
    const values = els.map(el => el.offsetTop)
    // console.log(values);获取每个元素div 高度 并返回一个数组
    //2.根据newValue去匹配想要的索引
    let index = values.length - 1
    for (let i = 0; i < values.length; i++) {
        if (values[i] > newValue + 44) {
            index = i - 1;
            break;
        }
    }
    tabControlRef.value?.setCurrentIndex(index)
})






</script>
<style lang="less" scoped>
.tabs {
    position: fixed;
    z-index: 9;
    top: 0;
    right: 0;
    left: 0;
}
.footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 120px;

    img {
        width: 123px;
    }
    .text {
        margin-top: 12px;
        font-size: 12px;
        color: #7688a7;
    }
}
</style>