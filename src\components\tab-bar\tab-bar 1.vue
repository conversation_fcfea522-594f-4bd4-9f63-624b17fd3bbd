<template>
    <div class="tab-bar">
        <!-- <div class="tab-bar-item">
            <img src="@/assets/img/tabbar/tab_home.png" alt="">
            <span class="text">首页</span>
        </div>
        <div class="tab-bar-item">
            <img src="@/assets/img/tabbar/tab_favor.png" alt="">
            <span class="text">收藏</span>
        </div>
        <div class="tab-bar-item">
            <img src="@/assets/img/tabbar/tab_order.png" alt="">
            <span class="text">订单</span>
        </div>
        <div class="tab-bar-item">
            <img src="@/assets/img/tabbar/tab_message.png" alt="">
            <span class="text">消息</span>
        </div> -->


        <template v-for="(item, index) in tabbarData" :key="item">
            <div class="tab-bar-item" :class="{ active: currentIndex === index }" @click="itemClick(index, item)">
                <img v-if="currentIndex !== index" :src="getAssetURL(item.image)" alt="">
                <img v-else :src="getAssetURL(item.imageActive)" alt="">
                <span class="text">{{ item.text }}</span>
            </div>
        </template>
    </div>
</template>
<script setup>
import tabbarData from "@/assets/data/tabbar"
import { getAssetURL } from "@/utils/load-assets"
import { ref } from "vue";
import { useRouter } from "vue-router";

const currentIndex = ref(0)
const router = useRouter()
const itemClick = (index, item) => {   // 相当于 function itemClick(){}
    currentIndex.value = index
    router.push(item.path)
}
</script>

<style lang="less" scoped>
.tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    display: flex;

    border-top: 1px solid #f3f3f3;

    .tab-bar-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        &.active {
            // color: var(--primary-color);
            color: #ff9854;
        }


        img {
            width: 36px;
        }

        .text {
            font-style: 12px;
            margin-top: 2px;
        }

    }
}
</style>