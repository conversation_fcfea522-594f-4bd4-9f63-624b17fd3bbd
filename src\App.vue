<template>
  <div>
    <!-- <h2>app vue</h2> -->
    <router-view v-slot="props">
      <keep-alive include="home">
        <component :is="props.Component"></component>
      </keep-alive>
    </router-view>
    <!-- <router-link to="/home">首页</router-link>
    <router-link to="/favor">收藏</router-link>
    <router-link to="/order">订单</router-link>
    <router-link to="/message">消息</router-link> -->
    <tabbar v-show="!route.meta.hideTabBar"></tabbar>
    <!-- <tabbar v-if="!$route.meta.hideTabBar"></tabbar> -->

    <!-- 数据加载中显示 -->
    <loading></loading>
  </div>
</template>

<script setup>
import tabbar from "@/components/tab-bar/tab-bar.vue"
import { useRoute } from "vue-router";
import loading from "./components/loading/loading.vue";
// 路由的映射
const route = useRoute()
</script>

<style lang="less" scoped></style>