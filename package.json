{"name": "11-<PERSON><PERSON>n-<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.3.4", "dayjs": "^1.11.7", "less": "^4.1.3", "normalize.css": "^8.0.1", "pinia": "^2.0.33", "postcss-px-to-viewport": "^1.1.1", "underscore": "^1.13.6", "vant": "^4.1.2", "vue": "^3.2.47", "vue-router": "^4.1.6"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "unplugin-vue-components": "^0.24.1", "vite": "^4.1.4"}}