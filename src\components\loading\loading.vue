<template>
    <div class="loading" v-if="MainStore.isLoading" @click="locadingClick">
        <div class="bg">
            <img src="@/assets/img/home/<USER>" alt="">
        </div>
    </div>
</template>

<script setup>

import useMainStore from '@/stores/modules/main';
const MainStore = useMainStore()
    const locadingClick = ()=>{
        MainStore.isLoading=false
    }
</script>

<style lang="less" scoped>
.loading {
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, .1);


    .bg {
        // position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 104px;
        height: 104px;
        background: url(@/assets/img/home/<USER>/ 100% 100%;

        img {
            // position: absolute;
            width: 65%;
            height: 65%;
            margin-bottom: 10px;
            // left: 18px;
            // top: 14px;
        }
    }
}
</style>