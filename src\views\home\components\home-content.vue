<template>
    <div class="content">
        <div class="title">热门精选</div>
        <div class="list">
            <template v-for="item in houselist" :key="item.data.houseId">
                <homeItemV9 v-if="item.discoveryContentType === 9" :itemData="item.data" @click="itemClick(item.data)" />
                <homeItemV3 v-else-if="item.discoveryContentType === 3" :itemData="item.data"
                    @click="itemClick(item.data)" />
            </template>
        </div>
    </div>
</template>

<script setup>
import homeItemV9 from '@/components/home-item-v9/home-item-v9.vue'
import homeItemV3 from '@/components/home-item-3/home-item-v3.vue'
import useHomeStore from '@/stores/modules/home';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
const homeStore = useHomeStore();
const { houselist } = storeToRefs(homeStore)

const router = useRouter()
const itemClick = (item) => {
    //跳转到Detail 页面
    router.push("/detail/" + item.houseId)
}


</script>

<style lang="less" scoped>
.content {
    padding: 10px 8px;

    .title {
        font-size: 22px;
        padding: 5px;
    }
}

.list {
    display: flex;
    flex-wrap: wrap;
}
</style>