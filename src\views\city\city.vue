<template>
    <div class="city top-page">
        <!-- 选择城市区域 -->
        <div class="top">
            <!-- 搜索框 -->
            <van-search v-model="searchValue" placeholder="城市/区域/位置" shape="round" show-action @cancel="cancelClick" />
            <!-- tab切换 选择城市页面 -->
            <van-tabs v-model:active="tabActive" color="#ff9845" line-height="2">
                <div v-for="(item, key, index) in  allCities " :key="key">
                    <van-tab :title="item.title" :name="key"></van-tab>
                </div>
            </van-tabs>
        </div>
        <!-- 选择城市区域下面的滚动区域 -->
        <div class="content">
            <!-- <template v-for="item in allCities[tabActive]?.cities " :key="item.id"> -->

            <template v-for="(value, key, index) in allCities" :key="value">
                <citygroup v-show="tabActive === key" :groupData="value"></citygroup>
            </template>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router';
import useCityStore from '@/stores/modules/city'
import { storeToRefs } from 'pinia';
import citygroup from '@/views/city/components/city-group.vue'
//搜索框
const router = useRouter()
const searchValue = ref("")
const cancelClick = () => {
    router.back()
}

// tab的切换
const tabActive = ref()

// 网络请求：请求城市的数据
// const allcity = ref({})
// getCityAll().then(res => {
//     console.log(res);
//     allcity.value = res.data
// })

// 从store中获取数据
const cityStore = useCityStore()
cityStore.fetchAllCitiesData()
const { allCities } = storeToRefs(cityStore)

// 获取选中标签后的数据
const currentGroup = computed(() => allCities.value[tabActive.value])






</script>

<style lang="less" scoped>
.city {

    .top{
        position: relative;
        z-index: 9;
    }
    .content {
        height: calc(100vh - 98px);
        overflow-y: auto;
    }



}
</style>