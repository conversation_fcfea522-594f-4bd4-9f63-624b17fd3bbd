import { defineStore } from "pinia";
import { getHomeHotSuggests, getHomeCategories, getHomeHouseList } from '@/services'
const useHomeStore = defineStore("home", {
    state: () => ({
        hotSuggests: [],
        categories: [],
        currentPage: 1,
        houselist: []
    }),

    actions: {
        async fetchHotSuggestsData() {
            const res = await getHomeHotSuggests();
            this.hotSuggests = res.data;
        },
        async fetchHotCategoriesData() {
            const res = await getHomeCategories();
            this.categories = res.data;
        },
        async fetchHouselistData(currentPage) {
            const res = await getHomeHouseList(this.currentPage);
            this.houselist.push(...res.data);
            this.currentPage++;
            // console.log(res);
        }
    }
})

export default useHomeStore