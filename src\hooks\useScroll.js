import { ref, onUnmounted, onMounted } from "vue";
import { throttle } from "underscore";

export function useScroll(elRef) {
    let el = window
    const isReachBottom = ref(false)
    const clienHeight = ref(0)  //当前界面的高度
    const scrollTop = ref(0)    //当前滚动了多少
    const scrollHeight = ref(0) //总共可以滚动的高度
    // 监听win窗口滚动  throttle 节流/防抖
    const scrollListenerHandler = throttle(() => {
        if (el === window) {
            clienHeight.value = document.documentElement.clientHeight + 1;    //当前界面的高度
            scrollTop.value = document.documentElement.scrollTop;            // 滚动的高度
            scrollHeight.value = document.documentElement.scrollHeight;       // 总共可以滚动的高度
        } else {
            clienHeight.value = el.clientHeight+3
            scrollTop.value = el.scrollTop
            scrollHeight.value = el.scrollHeight;
        }
        // console.log(clienHeight.value, scrollTop.value, scrollHeight.value);
        if (clienHeight.value + scrollTop.value >= scrollHeight.value) {
            isReachBottom.value = true
            // homeStore.fetchHouselistData();
            // console.log("加载更多数据");
        }
    }, 100)

    // el.addEventListener("scroll", scrollListenerHandler)
    // onMounted(() => {
    //     if (elRef) {
    //         el.value = elRef
    //     }
    //     el.addEventListener("scroll", scrollListenerHandler)
    // }),
    onMounted(() => {
        if (elRef) el = elRef.value
        el.addEventListener("scroll", scrollListenerHandler)
      })
        onUnmounted(() => {
            el.removeEventListener("scroll", scrollListenerHandler)
        })
    return { isReachBottom, scrollTop, clienHeight, scrollHeight }
}




//  老师代码

// import { onDeactivated, onMounted, onUnmounted, ref } from 'vue';
// import { throttle } from 'underscore'


// export default function useScroll(elRef) {
//   let el = window

//   const isReachBottom = ref(false)

//   const clientHeight = ref(0)
//   const scrollTop = ref(0)
//   const scrollHeight = ref(0)

//   // 防抖/节流
//   const scrollListenerHandler = throttle(() => {
//     if (el === window) {
//       clientHeight.value = document.documentElement.clientHeight
//       scrollTop.value = document.documentElement.scrollTop
//       scrollHeight.value = document.documentElement.scrollHeight
//     } else {
//       clientHeight.value = el.clientHeight
//       scrollTop.value = el.scrollTop
//       scrollHeight.value = el.scrollHeight
//     }
//     if (clientHeight.value + scrollTop.value >= scrollHeight.value) {
//       console.log("滚动到底部了")
//       isReachBottom.value = true
//     }
//   }, 100)
  
//   onMounted(() => {
//     if (elRef) el = elRef.value
//     el.addEventListener("scroll", scrollListenerHandler)
//   })
  
//   onUnmounted(() => {
//     el.removeEventListener("scroll", scrollListenerHandler)
//   })

//   return { isReachBottom, clientHeight, scrollTop, scrollHeight }
// }








